package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 监测图形-分布图--x轴
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-分布图-x轴")
@Data
public class PointDataDistributionChartXaxisVO {

    @Schema(description = "类型")
    private String type;

    @Schema(description = "边界间隔")
    private Boolean boundaryGap;

    @Schema(description = "刻度")
    private PointDataDistributionChartAxisLabelVO axisLabel;

    @Schema(description = "数据")
    private List<String> data;

    @Schema(description = "轴线")
    private PointDataDistributionChartXaxisLineVO axisLine;

    @Schema(description = "位置")
    private String position;
}
