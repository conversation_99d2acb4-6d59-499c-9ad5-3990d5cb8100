package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 监测图形-过程线--图例
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-过程线-图例")
@Data
public class PointDataProcessLineLegendVO {

    @Schema(description = "图例数据")
    private List<String> data;

}
