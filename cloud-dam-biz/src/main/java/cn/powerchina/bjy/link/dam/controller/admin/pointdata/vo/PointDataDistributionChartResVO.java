package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 监测图形-分布图的响应
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/26
 */
@Schema(description = "管理后台-监测图形-分布图 Response VO")
@Data
@ToString(callSuper = true)
public class PointDataDistributionChartResVO {

    @Schema(description = "标题")
    private PointDataDistributionChartTitleVO title;

    @Schema(description = "鼠标提示")
    private PointDataDistributionChartTootipVO tootip;

    @Schema(description = "图例")
    private PointDataDistributionChartLegendVO legend;

    @Schema(description = "x轴")
    private PointDataDistributionChartXaxisVO xAxis;

    @Schema(description = "y轴")
    private PointDataDistributionChartYaxisVO yAxis;

    @Schema(description = "数据")
    private List<PointDataDistributionChartSeriesVO> series;
}
