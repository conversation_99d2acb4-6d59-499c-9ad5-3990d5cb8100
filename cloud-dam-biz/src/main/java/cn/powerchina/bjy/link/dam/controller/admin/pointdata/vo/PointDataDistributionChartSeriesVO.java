package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 监测图形-分布图--数据
 * @Author: ya<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-分布图-数据")
@Data
public class PointDataDistributionChartSeriesVO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "标记大小")
    private Integer symbolSize;

    @Schema(description = "数据样式")
    private PointDataDistributionChartItemStyleVO itemStyle;

    @Schema(description = "数据")
    private List<BigDecimal> data;

}
