package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 监测图形-分布图--x轴--轴线
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-分布图-x轴-轴线")
@Data
public class PointDataDistributionChartXaxisLineVO {

    @Schema(description = "是否在0刻度上")
    private Boolean onZero;

    @Schema(description = "是否显示")
    private Boolean show;
}
