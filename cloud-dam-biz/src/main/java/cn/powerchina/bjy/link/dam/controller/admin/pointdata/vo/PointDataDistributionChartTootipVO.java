package cn.powerchina.bjy.link.dam.controller.admin.pointdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 监测图形-分布图--鼠标提示
 * @Author: yang<PERSON><PERSON><PERSON>
 * @CreateDate: 2025/06/30
 */
@Schema(description = "管理后台-监测图形-分布图-鼠标提示")
@Data
public class PointDataDistributionChartTootipVO {

    @Schema(description = "触发方式")
    private String trigger;

}
